import cv2
import numpy as np

def pencil_sketch(img_rgb):
    # 1. تحويل الصورة إلى تدرج الرمادي
    img_gray = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2GRAY)

    # 2. عكس الألوان في الصورة الرمادية
    img_invert = cv2.bitwise_not(img_gray)

    # 3. تنعيم الصورة المعكوسة باستخدام فلتر Gaussian Blur
    # هذا يسمح بحواف "ناعمة" مثل الرسم بالقلم الرصاص
    img_blur = cv2.GaussianBlur(img_invert, (21, 21), 0)

    # 4. عكس الألوان مرة أخرى لتنعيم التظليل
    img_sketch_invert = cv2.bitwise_not(img_blur)

    # 5. دمج الصورة الرمادية الأصلية مع الصورة الأخيرة المعكوسة
    # cv2.divide يعمل على المزج بطريقة تعطي تأثير الرسم بالقلم
    img_sketch = cv2.divide(img_gray, img_sketch_invert, scale=256.0)
    
    return img_sketch

# 1. قراءة الصورة
image_path = 'city.jpg' # استبدل 'your_image.jpg' باسم ملف صورتك
img = cv2.imread(image_path)

if img is None:
    print(f"Error: Could not load image from {image_path}")
else:
    # تطبيق التأثير
    sketch_img = pencil_sketch(img)

    # حفظ الصورة المعدلة
    cv2.imwrite('pencil_sketch_output.jpg', sketch_img)
    print("Pencil sketch saved successfully as: pencil_sketch_output.jpg")

    # عرض الصور (اختياري)
    cv2.imshow('Original Image', img)
    cv2.imshow('Pencil Sketch Effect', sketch_img)

    # انتظار ضغطة مفتاح وإغلاق النوافذ
    cv2.waitKey(0)
    cv2.destroyAllWindows()